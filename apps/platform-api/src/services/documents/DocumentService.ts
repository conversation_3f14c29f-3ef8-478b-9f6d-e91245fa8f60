import { createHash } from 'node:crypto';
import { ReadStream } from 'node:fs';
import { DocumentField, DocumentTag, Summary } from '@bybeam/doctopus-types';
import { allowedExtensions } from '@bybeam/platform-lib/verification/allowedExtensions';
import {
  ALLOWED_MIME_TYPES,
  Document,
  DocumentRelation,
  DocumentRelationType,
  DocumentRelationTypes,
  FeatureName,
  LinkToLegacyUserInput,
  MutationResponse,
  Nullable,
  PinDocumentInput,
  SubmitPredictionFeedbackInput,
  TopicName,
  UploadDocumentsInput,
} from '@bybeam/platform-types';
import { AdminToken, LoginToken } from '@platform-api/@types/authentication.js';
import {
  DocumentFieldRepository,
  DocumentRepository,
  DocumentSummaryRepository,
  DocumentTagRepository,
  PredictionFeedbackRepository,
} from '@platform-api/@types/repositories/index.js';
import { UploadRepository } from '@platform-api/@types/upload.js';
import { FileUpload } from 'graphql-upload/Upload.mjs';
import heic from 'heic-convert';
import sanitize from 'sanitize-filename';
import { v4 as uuidV4 } from 'uuid';
import { DocumentService as IDocumentService, MessagingService } from '../../@types/services.js';
import { logger } from '../../utils/logger.js';
import * as response from '../../utils/response.js';
import QueryServiceImplementation from '../QueryService.js';
import DuplicateService from '../duplicates/DuplicateService.js';
import PartnerService from '../partners/PartnerService.js';
import ProgramService from '../programs/ProgramService.js';
import ManyToOneDataLoader from '../utils/ManyToOneDataLoader.js';
import OneToOneDataLoader from '../utils/OneToOneDataLoader.js';

function uploadPrefix({ type, id }: DocumentRelation): string {
  switch (type) {
    case 'application':
      return `applications/${id}`;
    case 'case':
      return `cases/${id}`;
    case 'profile':
      return `profiles/${id}`;
    case 'program':
      return `programs/${id}`;
    case 'vendor':
      return `vendors/${id}`;
    case 'user':
      return `users/${id}`;
  }
}

type DocumentRelationDataloaderMap = {
  [relation in DocumentRelationType]: OneToOneDataLoader<
    { id: string; documentIds: string[] },
    { id: string; documentIds: string[] }
  >;
};

export const FILE_TYPE_ERROR_MESSAGE =
  'Only images, pdfs, word documents, or excel files are allowed';

export default class DocumentService
  extends QueryServiceImplementation<Document, DocumentRepository>
  implements IDocumentService
{
  private duplicateService: DuplicateService;
  private feedbackRepository: PredictionFeedbackRepository;
  private messagingService: MessagingService;
  private partnerService: PartnerService;
  private programService: ProgramService;
  private summaryRepository: DocumentSummaryRepository;
  private documentTagRepository: DocumentTagRepository;
  private documentFieldRepository: DocumentFieldRepository;
  private uploadRepository: UploadRepository;

  constructor({
    documentRepository,
    duplicateService,
    feedbackRepository,
    messagingService,
    partnerService,
    programService,
    summaryRepository,
    uploadRepository,
    documentFieldRepository,
    documentTagRepository,
  }: {
    documentRepository: DocumentRepository;
    duplicateService: DuplicateService;
    feedbackRepository: PredictionFeedbackRepository;
    messagingService: MessagingService;
    partnerService: PartnerService;
    programService: ProgramService;
    summaryRepository: DocumentSummaryRepository;
    documentTagRepository: DocumentTagRepository;
    documentFieldRepository: DocumentFieldRepository;
    uploadRepository: UploadRepository;
  }) {
    super(documentRepository);
    this.duplicateService = duplicateService;
    this.feedbackRepository = feedbackRepository;
    this.messagingService = messagingService;
    this.partnerService = partnerService;
    this.programService = programService;
    this.summaryRepository = summaryRepository;
    this.documentTagRepository = documentTagRepository;
    this.documentFieldRepository = documentFieldRepository;
    this.uploadRepository = uploadRepository;
  }

  private readonly relationToDocumentDataloaders = Object.fromEntries(
    DocumentRelationTypes.map((relation) => [
      relation,
      new OneToOneDataLoader(
        async (ids: string[]) => this.repository.findByRelationIds(ids, relation),
        (id) => ({ id, documentIds: [] }),
      ),
    ]),
  ) as unknown as DocumentRelationDataloaderMap;

  public async findByRelation(relation: DocumentRelation): Promise<Document[]> {
    const { documentIds } = await this.relationToDocumentDataloaders[relation.type].load(
      relation.id,
    );
    return Promise.all(documentIds.map((id) => this.findById(id)));
  }

  public async getPreviewUrl(
    { partnerId }: LoginToken,
    document: Document,
  ): Promise<Nullable<string>> {
    const partner = await this.partnerService.findById(partnerId);

    try {
      return await this.uploadRepository.getPreviewUrl({ partner, document });
    } catch (error) {
      logger.error({ error }, `${this.constructor.name}.getPreviewUrl: unexpected error >`);
      return '';
    }
  }

  public async pin({ id, pinned }: PinDocumentInput): Promise<MutationResponse<Document>> {
    const document = await this.repository.save({ id, pinned });
    return response.success({ recordId: id, record: document });
  }

  private async readFileToBuffer(file: FileUpload): Promise<Buffer> {
    const bufferArray = [];
    for await (const data of file.createReadStream()) {
      bufferArray.push(data);
    }
    return Buffer.concat(bufferArray);
  }

  private async sanitizeAndProcessFiles(
    files: Promise<FileUpload>[],
  ): Promise<Array<{ file: FileUpload; sha256: string }>> {
    return await Promise.all(
      files.map(async (filePromise) => {
        const rawFile = await filePromise;
        const sanitizedFilename = sanitize(rawFile.filename);
        const sanitizedFile = { ...rawFile, filename: sanitizedFilename };

        const buffer = await this.readFileToBuffer(sanitizedFile);

        // Handle HEIC conversion if needed
        const filetype = sanitizedFilename.split('.').pop();
        let processedFile = sanitizedFile;

        switch (filetype) {
          case 'heic':
          case 'heif':
            try {
              const convertedBuffer = await heic({ buffer, format: 'JPEG' });
              const filename = `${sanitizedFilename.substring(
                0,
                sanitizedFilename.lastIndexOf('.'),
              )}.jpeg`;

              processedFile = {
                createReadStream: () => ReadStream.from(Buffer.from(convertedBuffer)),
                mimetype: 'image/jpeg',
                filename,
                encoding: sanitizedFile.encoding,
              } as FileUpload;
            } catch (error) {
              logger.warn(
                { error },
                `${this.constructor.name}.convertFiles: failure converting ${sanitizedFilename}, leaving as ${filetype}`,
              );
              // Keep original file if conversion fails
            }
            break;
        }

        const hash = createHash('sha256');
        hash.update(buffer);
        const sha256 = hash.digest('hex');

        return { file: processedFile, sha256 };
      }),
    );
  }

  /**
   * Uploads and processes documents with classification and duplicate detection.
   *
   * PERFORMANCE CONCERNS: This function has become a complex synchronous orchestrator
   * that performs multiple sequential operations. Consider these improvement approaches:
   *
   * 1. **Message Queue Pipeline (Recommended)**:
   *    - Emit upload event → Queue workers handle: validation, processing, classification, duplicates
   *    - Pros: Better scalability, fault tolerance, retry mechanisms, backpressure handling
   *    - Cons: Eventual consistency, more complex infrastructure, harder debugging
   *
   * 2. **Event-Driven Architecture**:
   *    - Emit events after each step (uploaded, processed, classified, duplicates-checked)
   *    - Allows other services to react independently
   *    - Pros: Loose coupling, easier to extend, better observability
   *    - Cons: Eventual consistency, event ordering concerns
   *
   * 3. **Saga Pattern**:
   *    - Orchestrate as a series of compensatable transactions
   *    - Pros: Better error handling, rollback capabilities, distributed transaction support
   *    - Cons: More complex implementation, requires state management
   *
   * 4. **Async Optimization (Low-hanging fruit)**:
   *    - Run classification and duplicate detection in parallel
   *    - Batch database operations where possible
   *    - Use streaming for large file processing
   *
   * 5. **Staged Processing**:
   *    - Break into phases: validate → upload → persist → post-process
   *    - Allow resuming from checkpoints on failure
   *    - Pros: Better error recovery, easier testing, clearer flow
   *    - Cons: More complex state management
   *
   * Current bottlenecks: Sequential file processing, synchronous classification,
   * individual duplicate service calls, potential memory issues with large files.
   */
  public async upload(
    { userId: uploaderId, partnerId }: LoginToken,
    { relation, files, programId }: UploadDocumentsInput,
  ): Promise<Document[]> {
    const filesHaveError = await this.validateFileTypes(files);
    if (filesHaveError) {
      throw new Error(filesHaveError);
    }
    const partner = await this.partnerService.findById(partnerId);
    const processedFiles = await this.sanitizeAndProcessFiles(files);

    const uploads = await this.uploadRepository.upload({
      partner,
      prefix: uploadPrefix(relation),
      files: processedFiles.map(({ file }) => file),
    });

    const { identifiers } = await this.repository.insert(
      uploads.map((upload, index) => ({
        uploaderId,
        sha256: processedFiles[index].sha256,
        ...upload,
      })),
    );

    const documents = await this.findByIds(identifiers.map(({ id }) => id));

    const shouldClassify = await this.partnerService.hasFeature({
      partnerId,
      feature: FeatureName.DocumentsClassify,
    });
    if (shouldClassify) await this.classify(documents, programId);

    await Promise.all(
      documents.map(async (document) => {
        try {
          await this.duplicateService.recordDuplicateDocument(document);
        } catch (error) {
          logger.error(
            { error },
            `${this.constructor.name}.upload: error recording duplicate document`,
          );
        }
      }),
    );

    return documents;
  }

  public async validateFileTypes(files: Promise<FileUpload>[]): Promise<Nullable<string>> {
    let errorMessage = undefined;
    for (const file of await Promise.all(files)) {
      if (!allowedExtensions.test(file.filename) || !ALLOWED_MIME_TYPES.includes(file.mimetype)) {
        errorMessage = FILE_TYPE_ERROR_MESSAGE;
        logger.warn(
          `${this.constructor.name}.validateFileTypes: invalid file upload was attempted filename: ${file.filename} mimetype: ${file.mimetype}`,
        );
        break;
      }
    }
    return errorMessage;
  }

  public async relinkUser({ id, legacyUserId }: LinkToLegacyUserInput): Promise<string[]> {
    const ids = (
      await this.repository.find({ select: ['id'], where: { uploaderId: legacyUserId } })
    ).map(({ id }) => id);
    if (ids.length > 0)
      await this.repository.update({ uploaderId: legacyUserId }, { uploaderId: id });
    return ids;
  }

  public async classify(documents: Document[], programId?: string) {
    let modelId: string | undefined = undefined;
    if (programId) {
      const program = await this.programService.findById(programId);
      modelId = program.config?.documents?.modelId;
    }
    const messages = documents.map(({ id }) => ({
      document_id: id,
      ...(modelId && { model_id: modelId }),
    }));
    try {
      await this.messagingService.publishBulk({
        topicName: TopicName.DocUpload,
        messages,
      });
    } catch (error) {
      logger.warn({ error }, `${this.constructor.name}.classify: error publishing messages`);
    }
  }

  private readonly docIdToDocumentTagsDataloader = new ManyToOneDataLoader(
    (documentTag) => documentTag.documentId,
    async (ids: string[]) => this.documentTagRepository.findByDocumentIds(ids),
  );

  private readonly docIdToDocumentFieldsDataloader = new ManyToOneDataLoader(
    (documentField) => documentField.docId,
    async (ids: string[]) => this.documentFieldRepository.findByDocumentIds(ids),
  );

  public async getDocumentTags(id: string): Promise<DocumentTag[]> {
    return this.docIdToDocumentTagsDataloader.load(id);
  }

  public async getDocumentFields(id: string): Promise<DocumentField[]> {
    return this.docIdToDocumentFieldsDataloader.load(id);
  }

  private readonly docIdToSummaryDataloader = new OneToOneDataLoader(
    async (ids: string[]) => this.summaryRepository.findByDocumentIds(ids),
    () => undefined,
    (summary: Summary) => summary.documentId,
  );

  public async getSummary(id: string): Promise<Nullable<Summary>> {
    return this.docIdToSummaryDataloader.load(id);
  }

  public async submitPredictionFeedback(
    { adminId }: AdminToken,
    input: SubmitPredictionFeedbackInput,
  ): Promise<MutationResponse<Document>> {
    try {
      const { id, ...feedback } = input;
      const summary = await this.getSummary(input.id);
      if (!summary || !summary?.prediction)
        throw new Error(
          `${this.constructor.name}.submitPredictionFeedback: no summary or prediction found for document ${input.id}`,
        );

      const predictionId = summary.prediction.id;
      const existingFeedback = await this.feedbackRepository.findOne({
        where: { predictionId, adminId },
      });
      if (existingFeedback) {
        await this.feedbackRepository.softDelete({ id: existingFeedback.id });
      }

      const newFeedback = await this.feedbackRepository.save({
        id: uuidV4(),
        ...feedback,
        predictionId,
        adminId,
      });

      const record = await this.findById(input.id);
      return response.success({
        recordId: input.id,
        record: {
          ...record,
          summary: { ...summary, prediction: { ...summary.prediction, feedback: [newFeedback] } },
        },
      });
    } catch (e) {
      logger.error(
        { error: `${e}` },
        `${this.constructor.name}.submitPredictionFeedback: unexpected error`,
      );
      return response.error();
    }
  }
}
