import { QueryResponse } from '@bybeam/entity-resolution-client';
import { Document } from '@bybeam/platform-types';
import { mockLogger } from '@platform-api-test/setup/globals.js';
import {
  DuplicateDocumentRepository,
  DuplicateRepository,
} from '@platform-api/@types/repositories/core.js';
import EntityResolutionRepository from '@platform-api/repositories/external/EntityResolutionRespository.js';

import DuplicateService from './DuplicateService.js';

describe('DuplicateService', () => {
  describe('constructor', () => {
    it('should initialize with provided dependencies', () => {
      const mockRepository = {} as unknown as DuplicateRepository;
      const mockDuplicateDocumentRepository = {} as unknown as DuplicateDocumentRepository;
      const mockEntityResolutionRepo = {} as unknown as EntityResolutionRepository;

      const service = new DuplicateService({
        repository: mockRepository,
        duplicateDocumentRepository: mockDuplicateDocumentRepository,
        entityResolutionRepo: mockEntityResolutionRepo,
      });

      expect(service.repository).toBe(mockRepository);
      // biome-ignore lint/complexity/useLiteralKeys: private property
      expect(service['duplicateDocumentRepository']).toBe(mockDuplicateDocumentRepository);
      // biome-ignore lint/complexity/useLiteralKeys: private property
      expect(service['entityResolutionRepo']).toBe(mockEntityResolutionRepo);
    });
  });

  describe('recordDuplicateDocument', () => {
    let mockRepository: DuplicateRepository;
    let mockDuplicateDocumentRepository: DuplicateDocumentRepository;
    let mockEntityResolutionRepo: EntityResolutionRepository;
    let mockTransaction: ReturnType<typeof vi.fn>;
    let service: DuplicateService;

    beforeEach(() => {
      mockTransaction = vi.fn();

      mockRepository = {
        manager: {
          transaction: mockTransaction,
        },
      } as unknown as DuplicateRepository;

      mockDuplicateDocumentRepository = {
        insert: vi.fn(),
      } as unknown as DuplicateDocumentRepository;

      mockEntityResolutionRepo = {
        query: vi.fn(),
      } as unknown as EntityResolutionRepository;

      service = new DuplicateService({
        repository: mockRepository,
        duplicateDocumentRepository: mockDuplicateDocumentRepository,
        entityResolutionRepo: mockEntityResolutionRepo,
      });

      // Clear mocks
      vi.clearAllMocks();
    });

    describe('when document has no sha256 hash', () => {
      it('should throw an error', async () => {
        const document: Document = {
          id: 'doc-1',
          // sha256 is undefined
        } as Document;

        await expect(service.recordDuplicateDocument(document)).rejects.toThrow(
          'Document must have a sha256 hash',
        );

        expect(mockEntityResolutionRepo.query).not.toHaveBeenCalled();
        expect(mockTransaction).not.toHaveBeenCalled();
      });
    });

    describe('when no duplicates are found', () => {
      it('should return early without saving duplicates', async () => {
        const document: Document = {
          id: 'doc-1',
          sha256: 'test-hash-123',
        } as Document;

        const mockQueryResponse = {
          matches: [],
        } as unknown as QueryResponse;

        vi.mocked(mockEntityResolutionRepo.query).mockResolvedValue(mockQueryResponse);

        await service.recordDuplicateDocument(document);

        expect(mockEntityResolutionRepo.query).toHaveBeenCalledWith({
          datasource: { database: { name: 'core', table: 'documents' }, source: 'database' },
          model: 'DUPLICATE_HASH',
          entities: [
            {
              entityId: 'doc-1',
              fields: [{ fieldName: 'sha256', value: 'test-hash-123', fieldType: 'HASH' }],
            },
          ],
          features: [{ queryField: 'sha256', candidateField: 'sha256', metric: 'EXACT_MATCH' }],
          _maxResults: 'maxResults',
          _confidenceThreshold: 'confidenceThreshold',
        });

        expect(mockLogger.debug).toHaveBeenCalledWith(
          { duplicates: mockQueryResponse },
          'recordDuplicateDocument: duplicates found',
        );

        expect(mockTransaction).not.toHaveBeenCalled();
      });
    });

    describe('when duplicates are found', () => {
      it('should save duplicate record and document associations', async () => {
        const document: Document = {
          id: 'doc-1',
          sha256: 'test-hash-123',
        } as Document;

        const mockMatches = [
          { entityId: 'doc-2', score: 1.0 },
          { entityId: 'doc-3', score: 0.95 },
        ];

        const mockQueryResponse = {
          matches: mockMatches,
        } as unknown as QueryResponse;

        vi.mocked(mockEntityResolutionRepo.query).mockResolvedValue(mockQueryResponse);

        // Mock the transaction to execute the callback immediately
        mockTransaction.mockImplementation(async (callback) => {
          const mockTransactionManager = {
            save: vi.fn(),
            insert: vi.fn(),
          };
          return await callback(mockTransactionManager);
        });

        // Mock randomUUID to return predictable value
        const mockUUID = 'test-uuid-123';
        vi.doMock('node:crypto', () => ({
          randomUUID: vi.fn().mockReturnValue(mockUUID),
        }));

        await service.recordDuplicateDocument(document);

        expect(mockEntityResolutionRepo.query).toHaveBeenCalledWith({
          datasource: { database: { name: 'core', table: 'documents' }, source: 'database' },
          model: 'DUPLICATE_HASH',
          entities: [
            {
              entityId: 'doc-1',
              fields: [{ fieldName: 'sha256', value: 'test-hash-123', fieldType: 'HASH' }],
            },
          ],
          features: [{ queryField: 'sha256', candidateField: 'sha256', metric: 'EXACT_MATCH' }],
          _maxResults: 'maxResults',
          _confidenceThreshold: 'confidenceThreshold',
        });

        expect(mockLogger.debug).toHaveBeenCalledWith(
          { duplicates: mockQueryResponse },
          'recordDuplicateDocument: duplicates found',
        );

        expect(mockTransaction).toHaveBeenCalledWith(expect.any(Function));
      });
    });

    describe('when entity resolution query fails', () => {
      it('should propagate the error', async () => {
        const document: Document = {
          id: 'doc-1',
          sha256: 'test-hash-123',
        } as Document;

        const mockError = new Error('Entity resolution service unavailable');
        vi.mocked(mockEntityResolutionRepo.query).mockRejectedValue(mockError);

        await expect(service.recordDuplicateDocument(document)).rejects.toThrow(
          'Entity resolution service unavailable',
        );

        expect(mockTransaction).not.toHaveBeenCalled();
      });
    });

    describe('when transaction fails', () => {
      it('should propagate the error', async () => {
        const document: Document = {
          id: 'doc-1',
          sha256: 'test-hash-123',
        } as Document;

        const mockMatches = [{ entityId: 'doc-2', score: 1.0 }];
        const mockQueryResponse = { matches: mockMatches };

        vi.mocked(mockEntityResolutionRepo.query).mockResolvedValue(
          mockQueryResponse as unknown as QueryResponse,
        );

        const mockError = new Error('Transaction failed');
        mockTransaction.mockRejectedValue(mockError);

        await expect(service.recordDuplicateDocument(document)).rejects.toThrow(
          'Transaction failed',
        );

        expect(mockTransaction).toHaveBeenCalledWith(expect.any(Function));
      });
    });
  });
});
