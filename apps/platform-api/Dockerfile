FROM node:20.10.0 AS base

RUN corepack enable && corepack prepare pnpm@10.4.1 --activate 
ENV PNPM_HOME="/home/<USER>/.local/share/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

WORKDIR /app

FROM base AS builder

RUN pnpm install turbo@2.2.3 --global
COPY . .
# Entity resolution uses a slightly different structure where the protos located alongside the
# server instead of the client. This copies them into the expected location.
RUN mkdir -p packages/entity-resolution-client/protos
COPY apps/entity-resolution/protos/* packages/entity-resolution-client/protos/
RUN turbo prune @bybeam/platform-api --docker

FROM base AS installer

COPY --from=builder /app/out/json/ .
RUN pnpm install
 
COPY --from=builder /app/out/full/ .
RUN pnpm turbo run build --filter=@bybeam/platform-api
RUN pnpm install --prod

FROM gcr.io/distroless/nodejs20-debian12 AS runner
WORKDIR /app

ENV NODE_ENV production
COPY --from=installer /app ./run

# TODO improve container startup latency
CMD ["./run/apps/platform-api/dist/index.js"]
