import { Duplicate } from '@bybeam/platform-types';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('duplicates')
export class DuplicateEntity implements Duplicate {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column('uuid', { name: 'group_id' })
  public groupId: string;

  @Column('text', { name: 'entity_type' })
  public entityType: string;

  @Column('jsonb')
  public metadata: Record<string, unknown>;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  public createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  public updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deactivated_at' })
  public deactivatedAt?: Date;
}

export default DuplicateEntity;
